import { parse } from 'querystring';
export const getTableWidth = (columns: any[]) => {
  if (columns && Array.isArray(columns) && columns.length > 0) {
    let sum = 0;
    columns.forEach(({ width, hideInTable }) => {
      if (hideInTable) {
        sum += 0;
      } else if (Number.isFinite(width)) {
        sum += width;
      } else {
        // 如果width 不是number类型默认家100
        sum += 100;
      }
    });
    return sum;
  }
  return 1300;
};
/**
 * url中的查询字符串转化为json格式
 *
 * @param {string} [queryString] 待转换的查询串
 */
export const getQueryObj = (queryString?: string) =>
  parse(
    queryString || window.location.href.split('?')[1],
  ) as NodeJS.Dict<string>;
/** 运行环境类型 */
type PlatType =
  | 'com-wx-mobile'
  | 'wx-mobile'
  | 'mobile'
  | 'com-wx-pc'
  | 'wx-pc'
  | 'pc';

/**
 * 判断运行环境
 *
 * @return {*}
 */
export const envjudge = (): PlatType => {
  const isMobile = !!window.navigator.userAgent.match(
    /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i,
  ); // 是否手机端
  const isWx = /micromessenger/i.test(navigator.userAgent); // 是否微信
  const isComWx = /wxwork/i.test(navigator.userAgent); // 是否企业微信
  if (isMobile) {
    if (isComWx) {
      return 'com-wx-mobile'; // 手机端企业微信
    }
    if (isWx) {
      return 'wx-mobile'; // 手机端微信
    }
    return 'mobile'; // 手机
  }
  if (isComWx) {
    return 'com-wx-pc'; // PC端企业微信
  }
  if (isWx) {
    return 'wx-pc'; // PC端微信
  }
  return 'pc'; // PC
};

/**
 * 获取激活学年学期
 *
 * @param {API.XNXQ[]} list
 * @return {*}  {(API.XNXQ | null)}
 */
export const getCurrentXQ = (list: any): any => {
  if (!list || !Array.isArray(list) || !list.length) {
    return null;
  }
  const tempList = list.find((v: any) => v.lock === 1);
  return tempList;
};
/**
 * 获取认证cookie
 *
 * @return {*}
 */
export const getAuthCookie = (): string => {
  const reg = new RegExp('(^| )csrfToken=([^;]*)(;|$)');
  const arr = document.cookie.match(reg);
  if (arr) {
    return decodeURI(arr[2]);
  }
  return '';
};

/**
 * 从URL中获取查询参数
 * @param url 可选，默认为当前窗口的location.search
 * @returns 包含所有查询参数的对象
 */
export function getQueryParams(url?: string) {
  const searchParams = new URLSearchParams(url || window.location.search);
  const params: Record<string, string | null> = {};

  searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return params;
}
// 判断当前时间是否在时间数组内
export const getIsEffect = (timeArray: string[]) => {
  // 如果时间数组为空，代表整月可填，返回 true
  if (!timeArray || !Array.isArray(timeArray) || !timeArray.length) return true;

  // 将时间数组转换为 Date 对象
  const dateObjects = timeArray.map((time) => new Date(time));

  // 判断当前时间是否在时间数组内
  const today = new Date();
  const isCurrentTimeInArray = dateObjects.some(
    (date) =>
      date.getFullYear() === today.getFullYear() &&
      date.getMonth() === today.getMonth() &&
      date.getDate() === today.getDate(),
  );
  return isCurrentTimeInArray;
};
