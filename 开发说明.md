# 教师月度考核系统开发说明文档

## 项目概述

教师月度考核系统是一个基于React + TypeScript + Ant Design Pro的前端应用，用于管理教师的月度考核流程。系统支持考核方案配置、任务自动生成、多维度评分、结果统计和公示等功能。

## 技术栈

- **前端框架**: React 18 + TypeScript
- **UI组件库**: Ant Design Pro + ProComponents
- **构建工具**: UmiJS Max
- **状态管理**: UmiJS Model
- **样式**: Less

## 核心业务模块

### 1. 考核方案管理 (`src/pages/EvaluationPlan`)
- **考核方案** (`IAssessmentPlan`): 顶层配置，定义学期、评分类型、调整范围等
- **考核规则** (`IRule`): 二层配置，确定被评估者（角色/用户）
- **赋分规则** (`IScoring`): 三层配置，确定评估者和权重分配
- **观测点** (`IObservationPoint`): 四层配置，具体的评分项目

### 2. 填报评价 (`src/pages/FillEvaluation`)
- 考核任务列表和详情填写
- 支持观测点评分、附件上传、备注填写
- 实时计算总分和调整分数

### 3. 统计评价 (`src/pages/StatisticEvaluation`)
- 填报进度统计
- 问卷成绩管理（第三方接口集成）
- 考核结果汇总

### 4. 查询评价 (`src/pages/QueryEvaluation`)
- 个人查询：查看个人考核记录
- 考核公示：公开展示考核结果

## 关键数据结构

```typescript
interface IAssessmentPlan extends BaseEntity {
  planId: number;
  planName: string;
  enterpriseCode: string;
  semester: string;
  scoringType: 'score' | 'star';
  publicationType: 'grade' | 'score';
  status: 'draft' | 'published';
}

interface IAssessmentTask extends BaseEntity {
  taskId: number;
  assessorCode: string;
  assessedCode: string;
  month: number;
  status: 'pending' | 'completed';
}
```

## 特殊场景说明

### 1. 本地调试认证
系统使用加密串进行用户认证，本地调试时需要：

```javascript
// 1. 调用接口调度中心获取加密串
{
  "apiCode": "postDatacover",
  "body": {
    "enterpriseCode": "17157",
    "userCode": "47",
    "userType": "teacher"
  }
}

// 2. 将加密串作为URL参数
http://localhost:8000/?value=1234567890
```

### 2. 考核任务生成机制
- 系统根据已发布的考核方案自动生成月度任务
- 任务生成基于赋分规则中的评估者配置
- 一个被评估者可能对应多个评估任务

### 3. 评分计算逻辑
- 基础分：观测点分数 × 权重
- 调整分：受方案中 `maxAdjustment` 和 `minAdjustment` 限制
- 最终分数：各赋分规则加权求和

### 4. 第三方问卷集成
在统计模块中支持问卷成绩管理，用于集成外部评分数据：

```tsx
<QuestionScoreManager
  enterpriseCode={schoolInfo?.code || ''}
  semester={semester || ''}
  month={(month || 0) as number}
  ruleId={(ruleId || 0) as number}
/>
```

### 5. 权限控制
- 管理员：完整功能权限
- 普通用户：仅能查看个人相关数据
- 公示页面根据用户权限控制操作按钮显示

## API接口规范

### 核心接口
- `GET /assessment-plans`: 获取考核方案列表
- `GET /assessment-task/monthly/{enterpriseCode}/{semester}/{month}`: 获取月度任务
- `POST /score/tasks/{taskId}`: 提交评分数据

### 接口响应格式
```typescript
interface ResType<T = any> {
  errCode: number;
  data?: T;
  msg?: string;
}
```

## 部署配置

### 环境变量
```typescript
define: {
  TENCENT_COS: {
    SecretId: 'AKIDJRL2VjjU2JL7g06xZy84lErGSREl391e',
    SecretKey: 'ShhjY9WgfR1OuKVY1KPJ0SK0SgjY9e0B',
    baseDir: 'teacher-evaluation',
  },
}
```

### 主题配置
系统使用蓝色主题 (`#2F82FF`)，标题为"教师月度考核"。

## 开发注意事项

1. **数据层级关系**: 严格按照方案→规则→赋分→观测点的四层结构开发
2. **状态管理**: 考核任务状态变更需同步更新相关统计数据
3. **权限验证**: 所有操作都需要验证用户权限和数据归属
4. **错误处理**: 统一使用 `message.error()` 显示错误信息
5. **响应式设计**: 支持移动端适配，注意 `isMobile` 判断

## 项目结构

```
src/
├── components/          # 公共组件
│   ├── NoData/         # 空数据展示
│   └── ReportDetails/  # 考核详情组件
├── pages/              # 页面组件
│   ├── EvaluationPlan/ # 考核方案管理
│   ├── FillEvaluation/ # 填报评价
│   ├── StatisticEvaluation/ # 统计评价
│   ├── QueryEvaluation/ # 查询评价
│   └── NoAuth/         # 认证失败页面
├── services/           # API服务
├── utils/              # 工具函数
└── constants/          # 常量定义
```

## 常用命令

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build

# 代码格式化
pnpm format
```