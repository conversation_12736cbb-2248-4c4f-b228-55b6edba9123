import { getAllSemesterList } from '@/services/utils';
import { getCurrentXQ } from '@/utils/calc';
import {
  ModalForm,
  ModalFormProps,
  ProForm,
  ProFormDateRangePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { message } from 'antd';
import React, { useEffect, useRef } from 'react';

interface CopySchemeModalProps extends ModalFormProps {
  onCancel: () => void;
  data?: API.IAssessmentPlan;
}

export interface CopySchemeModalType {
  planName: string;
  semester: string;
  semesterName: string;
  startMonth: string;
  endMonth: string;
  description?: string;
}

const CopySchemeModal: React.FC<CopySchemeModalProps> = ({
  onCancel,
  data,
  ...restProps
}) => {
  const formRef = useRef<ProFormInstance>();
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};

  // 设置表单初始值
  useEffect(() => {
    if (data && restProps.open) {
      const initialData = {
        planName: `${data.planName} - 复制`,
        description: data.description || '',
      };

      const timer = setTimeout(() => {
        if (formRef.current) {
          formRef.current.setFieldsValue(initialData);
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [data, restProps.open]);

  const handleFinish = async (values: any) => {
    const { month, ...otherValues } = values;
    const [startMonth, endMonth] = month || [];
    
    if (!startMonth || !endMonth) {
      message.error('请选择执行月份');
      return false;
    }

    const submitData: CopySchemeModalType = {
      ...otherValues,
      startMonth: startMonth.toISOString(),
      endMonth: endMonth.toISOString(),
    };

    return restProps.onFinish?.(submitData);
  };

  return (
    <ModalForm
      width={600}
      formRef={formRef}
      title={`复制考核方案 - ${data?.planName || ''}`}
      {...restProps}
      layout="horizontal"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      onFinish={handleFinish}
      modalProps={{
        destroyOnClose: true,
        maskClosable: false,
        onCancel,
        styles: {
          body: {
            marginTop: '20px',
            maxHeight: '500px',
            overflowY: 'auto',
            overflowX: 'hidden',
            paddingRight: '5px',
          },
        },
      }}
    >
      <ProFormText hidden name="semesterName" />
      
      <ProFormText
        name="planName"
        label="方案名称"
        fieldProps={{
          maxLength: 100,
        }}
        rules={[
          {
            required: true,
            message: '请输入方案名称',
          },
        ]}
        placeholder="请输入新方案的名称"
      />

      <ProFormSelect
        name="semester"
        label="适用学期"
        rules={[
          {
            required: true,
            message: '请选择适用学期',
          },
        ]}
        request={async () => {
          const {
            errCode,
            data: semesterList,
            msg,
          } = await getAllSemesterList({
            enterpriseCode: schoolInfo?.code || '',
          });
          if (errCode) {
            message.warning('获取学年学期失败，' + msg);
            return [];
          }
          if (!semesterList?.list || semesterList.list.length < 0) {
            return [];
          }

          // 如果是新建（没有data），自动设置当前学期
          if (!data) {
            const curTerm = getCurrentXQ(semesterList.list);
            if (curTerm) {
              formRef.current?.setFieldsValue({
                semester: curTerm?.semester_code,
                semesterName: `${curTerm?.semester_code?.substring(
                  0,
                  4,
                )}-${curTerm?.semester_code?.substring(4, 8)} ${
                  curTerm?.semester_name
                }`,
              });
            }
          }

          return (semesterList?.list ?? [])?.map((item: any) => ({
            label: `${item.semester_code?.substring(
              0,
              4,
            )}-${item.semester_code?.substring(4, 8)} ${item.semester_name}`,
            value: item.semester_code,
          }));
        }}
        fieldProps={{
          onChange: (_value: string, option: any) => {
            if (formRef.current && option) {
              formRef.current.setFieldValue('semesterName', option.label);
            }
          },
        }}
        placeholder="请选择适用学期"
      />

      <ProFormDateRangePicker
        name="month"
        label="执行月份"
        fieldProps={{
          picker: 'month',
          format: 'YYYY-MM',
        }}
        rules={[
          {
            required: true,
            message: '请选择执行月份',
          },
        ]}
        placeholder={['开始月份', '结束月份']}
      />

      <ProFormTextArea
        name="description"
        label="方案描述"
        fieldProps={{
          maxLength: 500,
          rows: 4,
          showCount: true,
        }}
        placeholder="请输入方案描述（可选）"
      />

      <ProForm.Item label=" " colon={false}>
        <div style={{ color: '#666', fontSize: '12px' }}>
          <div>• 复制操作将创建一个新的考核方案</div>
          <div>• 新方案将包含原方案的所有配置规则</div>
          <div>• 复制后可以根据需要调整新方案的配置</div>
        </div>
      </ProForm.Item>
    </ModalForm>
  );
};

export default CopySchemeModal;
