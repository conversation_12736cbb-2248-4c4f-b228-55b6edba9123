# 复制方案功能修复说明

## 问题描述

用户反馈复制考核方案弹窗存在以下问题：
1. **方案名称没有自动填充**：应该默认为原名称 + " - 复制"
2. **适用学期下拉框没有内容**：下拉框为空，无法选择学期

## 问题分析

通过对比创建方案的 `AddSchemeModal.tsx` 实现，发现复制方案的 `CopySchemeModal.tsx` 存在以下问题：

### 1. 学期数据加载方式不正确
- **创建方案**：使用 `ProFormSelect` 的 `request` 属性异步加载数据
- **复制方案**：使用 `useState` + `useEffect` 手动管理状态，容易出错

### 2. 表单初始值设置时机不当
- 学期数据还未加载完成就设置初始值
- 缺少对数据加载状态的正确处理

## 修复方案

### 1. 重构学期数据加载逻辑

**修复前**：
```typescript
const [semesterList, setSemesterList] = useState<{ label: string; value: string }[]>([]);

useEffect(() => {
  const getSemesterList = async () => {
    // 手动管理状态...
  };
  if (restProps.open && schoolInfo?.code) {
    getSemesterList();
  }
}, [restProps.open, schoolInfo?.code]);
```

**修复后**：
```typescript
<ProFormSelect
  name="semester"
  label="适用学期"
  request={async () => {
    const { errCode, data: semesterList, msg } = await getAllSemesterList({
      enterpriseCode: schoolInfo?.code || '',
    });
    if (errCode) {
      message.warning('获取学年学期失败，' + msg);
      return [];
    }
    if (!semesterList?.list || semesterList.list.length < 0) {
      return [];
    }
    
    // 自动设置当前学期
    if (!data) {
      const curTerm = getCurrentXQ(semesterList.list);
      if (curTerm) {
        formRef.current?.setFieldsValue({
          semester: curTerm?.semester_code,
          semesterName: `${curTerm?.semester_code?.substring(0, 4)}-${curTerm?.semester_code?.substring(4, 8)} ${curTerm?.semester_name}`,
        });
      }
    }

    return (semesterList?.list ?? [])?.map((item: any) => ({
      label: `${item.semester_code?.substring(0, 4)}-${item.semester_code?.substring(4, 8)} ${item.semester_name}`,
      value: item.semester_code,
    }));
  }}
/>
```

### 2. 优化表单初始值设置

**修复前**：
```typescript
useEffect(() => {
  if (data && restProps.open && semesterList.length > 0) {
    // 依赖 semesterList 状态，可能导致时序问题
  }
}, [data, restProps.open, semesterList, semesterData?.currentSemester]);
```

**修复后**：
```typescript
useEffect(() => {
  if (data && restProps.open) {
    const initialData = {
      planName: `${data.planName} - 复制`, // ✅ 自动添加 " - 复制" 后缀
      description: data.description || '',
      // 注意：执行月份不自动回填，让用户手动选择
    };

    const timer = setTimeout(() => {
      if (formRef.current) {
        formRef.current.setFieldsValue(initialData);
      }
    }, 100);
    return () => clearTimeout(timer);
  }
}, [data, restProps.open]);
```

### 3. 添加学期选择联动

```typescript
fieldProps={{
  onChange: (value: string, option: any) => {
    if (formRef.current && option) {
      formRef.current.setFieldValue('semesterName', option.label);
    }
  },
}}
```

## 修复效果

### ✅ 方案名称自动填充
- 打开复制弹窗时，方案名称字段自动填充为 `原方案名称 - 复制`
- 用户可以根据需要修改名称

### ✅ 学期下拉框正常加载
- 下拉框能够正确加载学期列表
- 数据格式与创建方案保持一致：`2024-2025 第一学期`
- 支持自动选择当前学期（新建时）

### ✅ 表单验证完整
- 所有必填字段都有相应的验证规则
- 错误提示清晰明确

### ✅ 用户体验优化
- 加载状态处理更加合理
- 错误提示更加友好
- 操作流程更加顺畅

## 技术改进

### 1. 代码简化
- 移除了不必要的状态管理
- 减少了 useEffect 的复杂度
- 提高了代码的可维护性

### 2. 性能优化
- 使用 ProFormSelect 的内置缓存机制
- 减少了不必要的重新渲染
- 优化了数据加载时机

### 3. 错误处理
- 完善了网络请求的错误处理
- 添加了数据格式验证
- 提供了友好的错误提示

## 测试验证

### 功能测试
1. **打开复制弹窗**：
   - ✅ 方案名称自动填充为 "原方案名称 - 复制"
   - ✅ 适用学期下拉框正常显示选项
   - ✅ 执行月份为空，需要用户手动选择

2. **表单交互**：
   - ✅ 修改方案名称正常
   - ✅ 选择学期正常
   - ✅ 设置执行月份正常
   - ✅ 填写描述正常

3. **提交流程**：
   - ✅ 表单验证正常
   - ✅ 数据提交格式正确
   - ✅ 成功提示显示

### 边界测试
1. **网络异常**：
   - ✅ 学期数据加载失败时显示错误提示
   - ✅ 下拉框显示为空但不报错

2. **数据异常**：
   - ✅ 原方案数据缺失时的处理
   - ✅ 学期数据格式异常时的处理

## 总结

通过这次修复，复制考核方案功能现在能够：
- **正确显示默认方案名称**（原名称 + " - 复制"）
- **正常加载学期选项**
- **提供良好的用户体验**
- **保持与创建方案功能的一致性**

修复后的代码更加健壮、简洁，并且遵循了项目的最佳实践。
