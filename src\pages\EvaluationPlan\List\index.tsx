import SemesterSelect from '@/components/SemesterSelect';
import {
  checkEvaluationPlanAPI,
  copyEvaluationPlanAPI,
  createEvaluationPlanAPI,
  getAllEvaluationPlanAPI,
  removeEvaluationPlanAPI,
  updateEvaluationPlanAPI,
  updateEvaluationPlanStatusAPI,
} from '@/services/evaluation_plan';
import { PlusOutlined } from '@ant-design/icons';
import { ProTable } from '@ant-design/pro-components';
import { useModel } from '@umijs/max';
import { Button, message } from 'antd';
import React, { useEffect, useState } from 'react';
import type { AddSchemeModalType } from '../components/AddSchemeModal';
import AddSchemeModal from '../components/AddSchemeModal';
import type { CopySchemeModalType } from '../components/CopySchemeModal';
import CopySchemeModal from '../components/CopySchemeModal';
import CreateEvaluationRules from '../components/CreateEvaluationRules';
import columns from './columns';
import styles from './index.less';

const EvaluationPlanList: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const { schoolInfo } = initialState || {};
  const { semesterData } = useModel('global');
  const [schemeModalOpen, setschemeModalOpen] = useState({
    open: false,
    data: undefined,
  });
  const [drawerOpen, setDrawerOpen] = useState({
    open: false,
    data: undefined,
  });
  const [copyModalOpen, setCopyModalOpen] = useState({
    open: false,
    data: undefined,
  });
  const [currentSemester, setCurrentSemester] = useState(
    semesterData?.currentSemester ?? '',
  );
  const [dataSource, setDataSource] = useState<API.IAssessmentPlan[]>([]);
  /** 查询考核方案是否配置完整考核规则  */
  const [checkRulesId, setCheckRulesId] = useState<number[]>([]);
  const [checkingStatus, setCheckingStatus] = useState<
    Record<
      string,
      {
        status: 'checking' | 'complete' | 'incomplete' | 'error';
        retry?: () => void;
      }
    >
  >({});
  const [loadingMap, setLoadingMap] = useState<Record<string, boolean>>({});

  /** 获取考核列表 */
  const getEvaluationPlanList = async (isCheck = false) => {
    if (!currentSemester) return;
    const { errCode, data, msg } = await getAllEvaluationPlanAPI({
      enterpriseCode: schoolInfo?.code || '',
      semester: currentSemester,
    });
    if (errCode) {
      message.warning('获取考核列表数据失败，' + msg);
      setDataSource([]);
      return;
    }

    if (data?.list && data?.list?.length) {
      const initialList = data.list.map((item: any) => {
        // 保留之前的规则检测状态
        const existingItem: any = dataSource.find(
          (ds) => ds.planId === item.planId,
        );
        const rulesStatus =
          existingItem?.rulesStatus || (isCheck ? 'checking' : 'unchecked');
        const rules = existingItem?.rules || (isCheck ? '检查中...' : '未检查');

        return {
          ...item,
          rules,
          rulesStatus,
        };
      });

      if (isCheck) {
        const planIds = initialList.map((item: any) => item?.planId);
        setCheckRulesId(planIds);
        const initialStatus: Record<
          string,
          {
            status: 'checking' | 'complete' | 'incomplete' | 'error';
            retry?: () => void;
          }
        > = {};
        planIds.forEach((planId) => {
          initialStatus[planId] = { status: 'checking' };
        });
        setCheckingStatus(initialStatus);
      }
      setDataSource(initialList);
    } else {
      setDataSource([]);
    }
  };

  // 根据状态更新数据源
  const updateDataSourceWithStatus = (
    statusMap: Record<string, { status: string; retry?: () => void }>,
  ) => {
    const updatedDataSource = dataSource.map((item) => {
      const status = statusMap[item.planId];
      if (!status) return item;
      let rulesText = '检查中...';
      if (status.status === 'complete') {
        rulesText = '规则完整';
      } else if (status.status === 'incomplete') {
        rulesText = '规则不完整';
      } else if (status.status === 'error') {
        rulesText = '检查失败，点击重试';
      }

      return {
        ...item,
        rules: rulesText,
        rulesStatus: status.status,
        retryCheck: status.retry,
      };
    });

    setDataSource(updatedDataSource);
  };

  /** 删除考核方案 */
  const onRemoveScheme = async (values: AddSchemeModalType) => {
    const { planId } = values ?? {};
    if (!planId) {
      return message.warning('未读取到考核方案信息，请稍后重试');
    }
    const { errCode, msg } = await removeEvaluationPlanAPI(planId);
    if (errCode) {
      return message.warning('删除考核失败，' + msg);
    }
    message.success('删除考核成功');
    getEvaluationPlanList();
  };

  /** 新增考核方案 */
  const onAddScheme = async (values?: AddSchemeModalType) => {
    const { planStatus, fillableDates } = values ?? {};
    const { errCode, msg } = await createEvaluationPlanAPI({
      ...values,
      enterpriseCode: schoolInfo?.code || '',
      semester: currentSemester,
      month: undefined,
      planStatus: undefined,
      fillableDates: planStatus ? fillableDates || [] : [],
    });
    if (errCode) {
      return message.warning('新增考核方案失败，' + msg);
    }
    message.success('新增考核方案成功');
    getEvaluationPlanList(true);
    setschemeModalOpen({
      open: false,
      data: undefined,
    });
  };

  /** 编辑考核方案 */
  const onEditScheme = async (values?: AddSchemeModalType) => {
    const { planStatus, fillableDates, planId } = values ?? {};
    if (!planId) {
      return message.warning('未读取到考核方案信息，请稍后重试');
    }
    const { errCode, msg } = await updateEvaluationPlanAPI(planId, {
      ...values,
      planId: undefined,
      enterpriseCode: schoolInfo?.code || '',
      semester: currentSemester,
      month: undefined,
      planStatus: undefined,
      fillableDates: planStatus ? fillableDates || [] : [],
    });
    if (errCode) {
      return message.warning('编辑考核方案失败，' + msg);
    }
    message.success('编辑考核方案成功');
    getEvaluationPlanList();
    setschemeModalOpen({
      open: false,
      data: undefined,
    });
  };

  /** 复制考核方案 */
  const onCopyScheme = async (values?: CopySchemeModalType) => {
    if (!copyModalOpen.data?.planId) {
      return message.warning('未读取到原方案信息，请稍后重试');
    }

    const { errCode, msg, data } = await copyEvaluationPlanAPI(
      copyModalOpen.data.planId,
      values!,
    );

    if (errCode) {
      return message.warning('复制考核方案失败，' + msg);
    }

    message.success(`复制考核方案成功！新方案ID: ${data?.planId}`);
    getEvaluationPlanList(true);
    setCopyModalOpen({
      open: false,
      data: undefined,
    });
  };

  /** 启用/停用考核方案 */
  const onDeactivateEnableScheme = async (values: API.IAssessmentPlan) => {
    const { planId, status } = values ?? {};
    const isPublished = status === 'published';
    setLoadingMap((prev) => ({ ...prev, [planId]: true }));
    const { errCode, msg } = await updateEvaluationPlanStatusAPI(planId, {
      status: isPublished ? 'draft' : 'published',
    });
    if (errCode) {
      setLoadingMap((prev) => ({ ...prev, [planId]: false }));
      return message.warning(
        `${isPublished ? '停用' : '启用'}方案失败，` + msg,
      );
    }
    message.success(`${isPublished ? '停用' : '启用'}方案成功`);
    setLoadingMap((prev) => ({ ...prev, [planId]: false }));
    getEvaluationPlanList();
  };

  /** 检查考核规则是否完整 */
  const checkRules = async (planIdList: number[]) => {
    if (!planIdList?.length) return;
    const initialStatus = { ...checkingStatus };
    planIdList.forEach((planId) => {
      initialStatus[planId] = { status: 'checking' };
    });
    setCheckingStatus(initialStatus);
    const checkPromises = planIdList.map((planId) =>
      checkEvaluationPlanAPI(planId),
    );
    try {
      const results = await Promise.allSettled(checkPromises);
      const newStatus = { ...initialStatus };
      results.forEach((result, index) => {
        const planId = planIdList[index];
        if (result.status === 'rejected') {
          newStatus[planId] = {
            status: 'error',
            retry: () => checkRules([planId]),
          };
        } else {
          const response = result.value;
          if (response.errCode) {
            newStatus[planId] = {
              status: 'error',
              retry: () => checkRules([planId]),
            };
          } else {
            const isComplete = !!response.data;
            newStatus[planId] = {
              status: isComplete ? 'complete' : 'incomplete',
              retry: isComplete ? undefined : () => checkRules([planId]),
            };
          }
        }
      });
      setCheckingStatus(newStatus);
      updateDataSourceWithStatus(newStatus);
    } catch (error) {
      console.error('检查方案完整性时出错:', error);
    }
  };

  useEffect(() => {
    if (checkRulesId && checkRulesId?.length > 0) {
      checkRules(checkRulesId);
    }
  }, [checkRulesId]);

  useEffect(() => {
    getEvaluationPlanList(true);
  }, [currentSemester]);

  const handEvent = (type: string, data: any) => {
    switch (type) {
      case 'rules':
        setDrawerOpen({
          open: true,
          data,
        });
        break;
      case 'edit':
        setschemeModalOpen({
          open: true,
          data,
        });
        break;
      case 'copy':
        setCopyModalOpen({
          open: true,
          data,
        });
        break;
      case 'deactivateEnable':
        onDeactivateEnableScheme(data);
        break;
      case 'remove':
        onRemoveScheme(data);
        break;
      case 'retry':
        checkRules([data.planId]);
    }
  };

  return (
    <>
      <ProTable
        className={styles.evaluationPlanList}
        columns={columns({ handEvent, loadingMap })}
        cardBordered
        dataSource={dataSource}
        params={{ currentSemester }}
        columnsState={{
          persistenceKey: 'pro-table-singe-demos',
          persistenceType: 'localStorage',
          defaultValue: {
            option: { fixed: 'right', disable: true },
          },
        }}
        rowKey="id"
        search={false}
        options={false}
        form={{
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              };
            }
            return values;
          },
        }}
        pagination={{
          pageSize: 10,
        }}
        dateFormatter="string"
        headerTitle={
          <div className={styles.semester}>
            <SemesterSelect
              name="semester"
              label="学年学期"
              semesterChange={setCurrentSemester}
            />
          </div>
        }
        toolBarRender={() => [
          <Button
            key="button"
            icon={<PlusOutlined />}
            onClick={() => {
              setschemeModalOpen({
                open: true,
                data: undefined,
              });
            }}
            type="primary"
          >
            新增考核方案
          </Button>,
        ]}
      />
      <AddSchemeModal
        open={schemeModalOpen.open}
        data={schemeModalOpen.data}
        onFinish={!!schemeModalOpen.data ? onEditScheme : onAddScheme}
        onCancel={() => {
          setschemeModalOpen({
            open: false,
            data: undefined,
          });
        }}
      />
      <CopySchemeModal
        open={copyModalOpen.open}
        data={copyModalOpen.data}
        onFinish={onCopyScheme}
        onCancel={() => {
          setCopyModalOpen({
            open: false,
            data: undefined,
          });
        }}
      />
      <CreateEvaluationRules
        open={drawerOpen.open}
        data={drawerOpen.data || {}}
        onClose={() => {
          getEvaluationPlanList(true);
          setDrawerOpen({
            open: false,
            data: undefined,
          });
        }}
      />
    </>
  );
};
export default EvaluationPlanList;
