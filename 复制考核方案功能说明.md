# 复制考核方案功能实现说明

## 功能概述

根据后台提供的新接口 `POST /assessment-plans/:planId/copy`，在前端项目中实现了复制考核方案的功能。用户可以基于现有的考核方案快速创建新的方案，避免重复配置。

## 实现内容

### 1. API 接口封装

在 `src/services/evaluation_plan.ts` 中新增了复制方案的 API 接口：

```typescript
/** 复制考核方案 POST /assessment-plans/:planId/copy */
export async function copyEvaluationPlanAPI(
  planId: number,
  body: {
    planName: string;
    semester: string;
    semesterName: string;
    startMonth: string;
    endMonth: string;
    description?: string;
  },
) {
  return request<API.ResType<{
    planId: number;
    planName: string;
    message: string;
  }>>(`/assessment-plans/${planId}/copy`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}
```

### 2. 复制方案模态框组件

创建了 `src/pages/EvaluationPlan/components/CopySchemeModal.tsx` 组件，包含以下功能：

- **方案名称输入**：默认为原方案名称 + " - 复制"
- **学期选择**：支持选择目标学期
- **执行月份选择**：支持选择新方案的执行时间范围
- **方案描述**：可选的方案描述信息
- **表单验证**：确保必填字段的完整性

### 3. 操作按钮集成

在考核方案列表页面的操作列中添加了"复制"按钮：

- 位置：编辑按钮之后
- 样式：与其他操作按钮保持一致
- 权限：所有方案都可以复制

### 4. 事件处理逻辑

在 `src/pages/EvaluationPlan/List/index.tsx` 中实现了完整的复制流程：

- **状态管理**：添加了 `copyModalOpen` 状态控制模态框显示
- **事件处理**：在 `handEvent` 函数中添加了 'copy' 事件处理
- **复制逻辑**：实现了 `onCopyScheme` 函数处理复制请求
- **成功反馈**：复制成功后刷新列表并显示成功消息

## 使用流程

1. **进入考核方案列表页面**
2. **点击目标方案的"复制"按钮**
3. **在弹出的模态框中填写新方案信息**：
   - 修改方案名称（默认为原名称 + " - 复制"）
   - 选择适用学期
   - 设置执行月份范围
   - 填写方案描述（可选）
4. **点击确定完成复制**
5. **系统显示成功消息并刷新列表**

## 技术特点

### 用户体验优化

- **智能默认值**：自动填充合理的默认值
- **表单验证**：实时验证必填字段
- **操作提示**：清晰的功能说明和操作指引
- **响应式设计**：适配不同屏幕尺寸

### 代码质量

- **类型安全**：完整的 TypeScript 类型定义
- **组件复用**：复用现有的 ProComponents 组件
- **错误处理**：完善的错误提示和异常处理
- **代码规范**：遵循项目现有的代码风格

### 数据处理

- **日期格式化**：正确处理日期时间格式转换
- **学期联动**：学期选择时自动更新学期名称
- **状态管理**：合理的组件状态管理

## 后续扩展

该功能为基础实现，后续可以根据需求进行以下扩展：

1. **批量复制**：支持同时复制多个方案
2. **复制选项**：提供更多复制配置选项
3. **模板管理**：将常用配置保存为模板
4. **权限控制**：根据用户角色控制复制权限

## 测试建议

1. **功能测试**：验证复制流程的完整性
2. **边界测试**：测试各种边界情况和异常输入
3. **兼容性测试**：确保在不同浏览器中正常工作
4. **性能测试**：验证大量数据情况下的性能表现

## 注意事项

- 复制操作会创建全新的方案，包含原方案的所有配置规则
- 新方案的状态为草稿状态，需要手动启用
- 复制后建议检查和调整新方案的具体配置
- 确保目标学期和执行时间的合理性
