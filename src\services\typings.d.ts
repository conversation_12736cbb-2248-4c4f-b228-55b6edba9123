/* eslint-disable */
// 该文件由 OneAPI 自动生成，请勿手动修改！

declare namespace API {
  interface ResType<T = any> {
    errCode?: boolean;
    msg?: string;
    data?: T;
  }

  /**
   * 基础实体接口
   */
  interface BaseEntity {
    /** 创建时间 - 记录实体创建的日期时间 */
    createdAt?: Date;
    /** 更新时间 - 记录实体最后一次更新的日期时间 */
    updatedAt?: Date;
  }

  /**
   * 考核方案接口 - 第一层
   */
  interface IAssessmentPlan extends BaseEntity {
    /** 方案ID - 考核方案的唯一标识符 */
    planId: number;
    /** 方案名称 - 考核方案的名称 */
    planName: string;
    /** 学校编号 */
    enterpriseCode: string;
    /** 学期 - 考核方案所属的学期 */
    semester: string;
    /** 学期名称 - 考核方案所属的学期 */
    semesterName: string;
    /** 开始月份 - 考核方案的开始日期 */
    startMonth: Date;
    /** 结束月份 - 考核方案的结束日期 */
    endMonth: Date;
    /** 可填写日期 - 允许填写的日期数组，空数组表示整月都能填，数字数组表示只能在指定日期填写 */
    fillableDates: number[];
    /** 评分类型 - 分数制或星级制 */
    scoringType: 'score' | 'star';
    /** 最大调整值 - 评分的最大上调幅度 */
    maxAdjustment?: number;
    /** 最小调整值 - 评分的最大下调幅度 */
    minAdjustment?: number;
    /** 公示类型 - 分数显示方式，可选分数或等级 */
    publicationType: 'grade' | 'score';
    /** 最大星级数 - 星级制评分的最大星星数量 */
    maxStars?: number;
    /** 等次规则 */
    gradeRules?: {
      /** 等次名称 */
      gradeName: string;
      /** 起始分数 */
      startScore: number;
      /** 结束分数 */
      endScore: number;
      /** 对应等级 */
      grade: string;
    }[];
    /** 描述 - 考核方案的描述 */
    description?: string;
    /** 状态 - 考核方案的状态，可选值为 'draft'（草稿）或 'published'（已发布） */
    status: 'draft' | 'published';
  }

  /**
   * 考核规则接口 - 第二层，确定被评估者的角色或用户
   */
  interface IRule extends BaseEntity {
    /** 规则ID - 考核规则的唯一标识符 */
    ruleId: number;
    /** 方案ID - 关联的考核方案ID */
    planId: number;
    /** 规则名称 - 考核规则的名称 */
    title: string;
    /** 被评估类型 - 角色或用户 */
    assessedType: 'role' | 'user';
    /** 角色名称列表 - 被评估的角色列表，每个角色格式为{roleCode: 1, roleName: "管理员"} */
    roles?: {
      roleCode: string;
      roleName: string;
    }[];
    /** 用户ID列表 - 被评估的用户列表，每个用户格式为{userCode: 1, userName: "admin"} */
    users?: {
      userCode: string;
      userName: string;
    }[];
    assessmentPlan?: IAssessmentPlan;
    /** 描述 - 考核规则的描述 */
    description?: string;
  }

  /**
   * 赋分规则接口 - 第三层，确定评估者的角色或用户
   */
  interface IScoring extends BaseEntity {
    /** 赋分ID - 赋分规则的唯一标识符 */
    scoringId: number;
    /** 规则ID - 关联的考核规则ID */
    ruleId: number;
    /** 赋分名称 - 赋分规则的名称 */
    title: string;
    /** 评估者类型 - 角色、用户或混合 */
    assessorType: 'role' | 'user' | 'mixed' | 'question';
    /** 角色名称列表 - 评估的角色列表，每个角色格式为{roleCode: 1, roleName: "管理员"} */
    roles?: {
      roleCode: string;
      roleName: string;
    }[];
    /** 用户ID列表 - 评估的用户ID列表，每个用户格式为{userCode: 1, userName: "admin"} */
    users?: {
      userCode: string;
      userName: string;
    }[];
    /** 权重 - 该赋分在整体评分中的权重 */
    weight: number;
    /** 描述 - 赋分规则的描述 */
    description?: string;
    rule?: IRule;
  }

  /**
   * 观测点接口 - 第四层
   */
  interface IObservationPoint extends BaseEntity {
    /** 观测点ID - 观测点的唯一标识符 */
    pointId: number;
    /** 赋分ID - 关联的赋分规则ID */
    scoringId: number;
    /** 观测点名称 - 观测点的名称 */
    pointName: string;
    /** 描述 - 观测点的评分说明 */
    description?: string;
    /** 基础分值 - 该观测点的基础分数 */
    baseScore: number;
    /** 排序索引 - 观测点在赋分规则中的排序位置 */
    orderIndex: number;
    scoring?: IScoring;
  }

  /**
   * 考核任务接口
   */
  interface IAssessmentTask extends BaseEntity {
    /** 任务ID - 系统自动生成 */
    taskId: number;
    /** 考核方案ID */
    planId: number;
    /** 考核规则ID */
    ruleId: number;
    /** 赋分规则ID */
    scoringId: number;
    /** 考核月份 */
    month: number;
    /** 考核人编号或用户名 */
    assessorCode: string;
    /** 考核人姓名 */
    assessorName: string;
    /** 被考核人编号或用户名 */
    assessedCode: string;
    /** 被考核人姓名 */
    assessedName: string;
    /** 评分规则名称 */
    ruleName: string;
    /** 赋分规则名称 */
    scoringName: string;
    /** 任务状态 */
    status: 'pending' | 'completed';
    scoring?: IScoring;
    scores?: IScore[];
  }

  interface ITaskMonthItme {
    /** 已完成数量 */
    completedCount: string;
    /** 结束月份 */
    endMonth: string;
    /** 企业编码 */
    enterpriseCode: string;
    /** 可填写日期 */
    fillableDates: string[];
    /** 月份 */
    month: number;
    /** 未完成数量 */
    notCompletedCount: string;
    /** 方案ID */
    planId: number;
    /** 学期 */
    semester: string;
    /** 开始月份 */
    startMonth: string;
    /** 总数 */
    total: number;
    /** 年份 */
    year: number;
  }

  /**
   * 评分接口
   */
  interface IScore extends BaseEntity {
    /** 评分ID - 系统自动生成 */
    scoreId: number;
    /** 关联任务ID */
    taskId: number;
    /** 观测点ID */
    pointId: number;
    /** 实际评分值 */
    scoreValue?: number;
    /** 备注 */
    remark?: string;
    /** 附件 */
    files?: string[];
    observationPoint?: IObservationPoint;
  }

  /**
   * 公示记录接口
   */
  interface IPublicationRecord extends BaseEntity {
    /** 公示记录ID - 公示记录的唯一标识符 */
    pubId: number;
    /** 方案ID - 关联的考核方案ID */
    planId: number;
    /** 月份 - 公示的月份 */
    month: number;
    /** 开始时间 - 公示的开始日期时间 */
    startTime: Date;
    /** 结束时间 - 公示的结束日期时间 */
    endTime: Date;
  }

  /**
   * 评分报表接口
   */
  interface IScoreReport {
    /** 报表记录ID - 系统自动生成 */
    reportId: number;
    /** 学校编号 */
    enterpriseCode: string;
    /** 学年学期编号 */
    semester: string;
    /** 学年学期名称 */
    semesterName: string;
    /** 考核方案ID */
    planId: number;
    /** 考核方案名称 */
    planName: string;
    /** 考核规则ID */
    ruleId: number;
    /** 考核规则名称 */
    ruleName: string;
    /** 被考核人编号 */
    assessedCode: string;
    /** 被考核人姓名 */
    assessedName: string;
    /** 考核月份 */
    month: number;
    /** 评分 */
    score: number;
    assessmentPlan?: IAssessmentPlan;
    scoreReportDetail?: IScoreReportDetail[];
  }

  /**
   * 评分报表详情接口
   */
  interface IScoreReportDetail {
    /** 报表记录ID - 系统自动生成 */
    detailId: number;
    /** 主报表记录ID */
    reportId: number;
    /** 评分组ID */
    scoringId: number;
    /** 评分组名称 */
    scoringName: string;
    /** 是否是问卷类型 */
    isQuestion: boolean;
    /** 权重 */
    weight: number;
    /** 评分 */
    score: number;
  }
}
